"use client";

import Image from "next/image";
import Link from "next/link";
import { XCircle } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Product } from "@/types";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

interface ProductModalProps {
  product: Product | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ProductModal({ product, open, onOpenChange }: ProductModalProps) {
  if (!product) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] p-0 overflow-hidden bg-white">
        <div className="grid md:grid-cols-2 gap-0">
          <div className="relative aspect-square">
            <Image
              src={product.image}
              alt={product.name}
              fill
              className="object-cover"
            />
            <Button
              size="icon"
              variant="ghost"
              className="absolute top-2 right-2 rounded-full bg-white/80 hover:bg-white"
              onClick={() => onOpenChange(false)}
            >
              <XCircle className="h-5 w-5" />
              <span className="sr-only">Close</span>
            </Button>
          </div>
          <div className="p-6 space-y-4 flex flex-col">
            <DialogHeader className="space-y-1 text-left">
              <DialogTitle className="font-serif text-2xl">
                {product.name}
              </DialogTitle>
              <DialogDescription>
                {product.shortDescription}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 flex-grow">
              <div>
                <h4 className="font-medium mb-2">Description</h4>
                <p className="text-sm text-muted-foreground">{product.description}</p>
              </div>
              <Separator />
              <div className="grid grid-cols-2 gap-y-3 text-sm">
                <div>
                  <span className="font-medium">Category:</span>
                  <p className="text-muted-foreground capitalize">{product.category}</p>
                </div>
                {product.price && (
                  <div>
                    <span className="font-medium">Price:</span>
                    <p className="text-muted-foreground">
                      ${product.price} {product.priceUnit}
                    </p>
                  </div>
                )}
                <div>
                  <span className="font-medium">Available Colors:</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {product.colors.map((color) => (
                      <Badge key={color} variant="secondary" className="text-xs">
                        {color}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <span className="font-medium">Available Finishes:</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {product.finishes.map((finish) => (
                      <Badge key={finish} variant="outline" className="text-xs">
                        {finish}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            <DialogFooter className="sm:justify-start gap-2 mt-4">
              <Button asChild>
                <Link href={`/contact?product=${product.id}`}>Request Quote</Link>
              </Button>
              <Button variant="outline" asChild>
                <Link href={`/catalog/${product.id}`}>Full Details</Link>
              </Button>
            </DialogFooter>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}