"use client";

import type React from "react";

import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import type { Product } from "@/types";
import { DialogTitle } from "@radix-ui/react-dialog";
import { AnimatePresence, motion } from "framer-motion";
import { Mail, Phone, RotateCcw, ZoomIn, ZoomOut } from "lucide-react";
import Image from "next/image";
import { useCallback, useRef, useState } from "react";
import { Badge } from "./badge";

interface EnhancedProductModalProps {
   product: Product;
   isOpen: boolean;
   onClose: () => void;
}

export default function ProductModal({
   product,
   isOpen,
   onClose,
}: EnhancedProductModalProps) {
   const [scale, setScale] = useState(1);
   const [position, setPosition] = useState({ x: 0, y: 0 });
   const [isDragging, setIsDragging] = useState(false);
   const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
   const imageRef = useRef<HTMLDivElement>(null);

   const handleZoomIn = useCallback(() => {
      setScale((prev) => Math.min(prev * 1.5, 4));
   }, []);

   const handleZoomOut = useCallback(() => {
      setScale((prev) => Math.max(prev / 1.5, 1));
      if (scale <= 1.5) {
         setPosition({ x: 0, y: 0 });
      }
   }, [scale]);

   const handleReset = useCallback(() => {
      setScale(1);
      setPosition({ x: 0, y: 0 });
   }, []);

   const handleMouseDown = useCallback(
      (e: React.MouseEvent) => {
         if (scale > 1) {
            setIsDragging(true);
            setDragStart({
               x: e.clientX - position.x,
               y: e.clientY - position.y,
            });
         }
      },
      [scale, position],
   );

   const handleMouseMove = useCallback(
      (e: React.MouseEvent) => {
         if (isDragging && scale > 1) {
            const newX = e.clientX - dragStart.x;
            const newY = e.clientY - dragStart.y;

            // Constrain movement within bounds
            const maxX = (scale - 1) * 200;
            const maxY = (scale - 1) * 200;

            setPosition({
               x: Math.max(-maxX, Math.min(maxX, newX)),
               y: Math.max(-maxY, Math.min(maxY, newY)),
            });
         }
      },
      [isDragging, dragStart, scale],
   );

   const handleMouseUp = useCallback(() => {
      setIsDragging(false);
   }, []);

   return (
      <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent className="max-h-[90vh] w-[1024px] max-w-[90vw] overflow-auto border-none bg-white p-0 md:h-[600px]">
            <DialogTitle className="sr-only">Product Details</DialogTitle>
            <AnimatePresence>
               {isOpen && (
                  <motion.div
                     initial={{ opacity: 0, scale: 0.9 }}
                     animate={{ opacity: 1, scale: 1 }}
                     exit={{ opacity: 0, scale: 0.9 }}
                     transition={{ duration: 0.3, ease: "easeOut" }}
                     className="flex h-full flex-col md:flex-row"
                  >
                     {/* Image Section */}
                     <div className="relative flex-1 overflow-hidden bg-stone-50">
                        {/* Header */}
                        <div className="absolute left-0 right-0 top-0 z-20 bg-gradient-to-b from-black/50 to-transparent p-6">
                           <div className="flex items-center justify-between text-white">
                              <div>
                                 <h2 className="font-playfair text-2xl font-bold">
                                    {product.name}
                                 </h2>
                                 <Badge variant="secondary" className="mt-1">
                                    {product.category}
                                 </Badge>
                              </div>
                           </div>
                        </div>

                        {/* Image Container */}
                        <div
                           ref={imageRef}
                           className="relative h-full min-h-96 w-full cursor-grab overflow-hidden active:cursor-grabbing"
                           onMouseDown={handleMouseDown}
                           onMouseMove={handleMouseMove}
                           onMouseUp={handleMouseUp}
                           onMouseLeave={handleMouseUp}
                        >
                           <motion.div
                              className="relative h-full w-full"
                              animate={{
                                 scale,
                                 x: position.x,
                                 y: position.y,
                              }}
                              transition={{
                                 type: "spring",
                                 stiffness: 300,
                                 damping: 30,
                              }}
                           >
                              <Image
                                 src={product.image || "/placeholder.svg"}
                                 alt={product.name}
                                 fill
                                 className="select-none object-cover"
                                 draggable={false}
                                 priority
                              />
                           </motion.div>
                        </div>

                        {/* Zoom Controls */}
                        <div className="absolute bottom-6 left-6 z-20">
                           <div className="flex items-center gap-2 rounded-lg bg-white/90 p-2 shadow-lg backdrop-blur-sm">
                              <Button
                                 variant="ghost"
                                 size="icon"
                                 onClick={handleZoomIn}
                                 disabled={scale >= 4}
                                 className="h-8 w-8"
                              >
                                 <ZoomIn className="h-4 w-4" />
                              </Button>
                              <Button
                                 variant="ghost"
                                 size="icon"
                                 onClick={handleZoomOut}
                                 disabled={scale <= 1}
                                 className="h-8 w-8"
                              >
                                 <ZoomOut className="h-4 w-4" />
                              </Button>
                              <Button
                                 variant="ghost"
                                 size="icon"
                                 onClick={handleReset}
                                 disabled={
                                    scale === 1 &&
                                    position.x === 0 &&
                                    position.y === 0
                                 }
                                 className="h-8 w-8"
                              >
                                 <RotateCcw className="h-4 w-4" />
                              </Button>
                           </div>
                        </div>

                        {/* Zoom Indicator */}
                        {scale > 1 && (
                           <div className="absolute bottom-6 right-6 z-20">
                              <div className="rounded-lg bg-white/90 px-3 py-1 shadow-lg backdrop-blur-sm">
                                 <span className="text-sm font-medium">
                                    {Math.round(scale * 100)}%
                                 </span>
                              </div>
                           </div>
                        )}
                     </div>

                     {/* Action Panel */}
                     <div className="flex w-full flex-col border-l border-stone-200 bg-white md:w-80">
                        <div className="border-b border-stone-200 p-6">
                           <h3 className="font-playfair text-xl font-semibold text-stone-900">
                              {product.name}
                           </h3>
                           <p className="mt-1 text-stone-600">
                              {product.category}
                           </p>
                        </div>

                        <div className="flex-1 p-6">
                           <div className="space-y-6">
                              <div>
                                 <h4 className="mb-3 font-medium text-stone-900">
                                    Interested in this product?
                                 </h4>
                                 <p className="mb-4 text-sm text-stone-600">
                                    Contact us for pricing, availability, and
                                    custom sizing options.
                                 </p>
                              </div>

                              <div className="space-y-3">
                                 <Button
                                    asChild
                                    className="w-full bg-stone-800 hover:bg-stone-900"
                                 >
                                    <a href={`/contact?product=${product.id}`}>
                                       <Mail className="mr-2 h-4 w-4" />
                                       Request Quote
                                    </a>
                                 </Button>

                                 <Button
                                    asChild
                                    variant="outline"
                                    className="w-full"
                                 >
                                    <a href="tel:+12345678900">
                                       <Phone className="mr-2 h-4 w-4" />
                                       Call Now
                                    </a>
                                 </Button>

                                 <Button
                                    asChild
                                    variant="outline"
                                    className="w-full"
                                 >
                                    <a
                                       href="https://wa.me/1234567890"
                                       target="_blank"
                                       rel="noopener noreferrer"
                                    >
                                       <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          width="16"
                                          height="16"
                                          viewBox="0 0 24 24"
                                          fill="none"
                                          stroke="currentColor"
                                          strokeWidth="2"
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          className="mr-2"
                                       >
                                          <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                                       </svg>
                                       WhatsApp
                                    </a>
                                 </Button>
                              </div>
                           </div>
                        </div>

                        <div className="border-t border-stone-200 bg-stone-50 p-6">
                           <div className="text-center">
                              <p className="mb-2 text-xs text-stone-500">
                                 Need help choosing?
                              </p>
                              <Button
                                 asChild
                                 variant="link"
                                 className="h-auto p-0 text-sm text-stone-700"
                              >
                                 <a href="/contact">Speak with us</a>
                              </Button>
                           </div>
                        </div>
                     </div>
                  </motion.div>
               )}
            </AnimatePresence>
         </DialogContent>
      </Dialog>
   );
}
