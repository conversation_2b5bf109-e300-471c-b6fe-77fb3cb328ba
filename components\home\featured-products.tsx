"use client";

import { motion } from "framer-motion";
import { useState } from "react";

import { Button } from "@/components/ui/button";
import { ProductCard } from "@/components/ui/product-card";
// import { ProductModal } from "@/components/ui/product-modal";
import { SectionHeader } from "@/components/ui/section-header";
import { PRODUCTS } from "@/constants";
import { Product } from "@/types";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import ProductModal from "../ui/product-modal-2";

export function FeaturedProducts() {
   const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
   const [modalOpen, setModalOpen] = useState(false);

   const featuredProducts = PRODUCTS.filter((product) => product.featured);

   const handleViewProduct = (product: Product) => {
      setSelectedProduct(product);
      setModalOpen(true);
   };

   return (
      <section className="py-16 md:py-24 md:pb-4">
         <div className="container">
            <SectionHeader
               title="Our Featured Collection"
               subtitle="Discover our most popular natural stone selections, chosen for their exceptional beauty and quality."
               centered
            />

            <div className="mt-12 grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
               {featuredProducts.map((product) => (
                  <ProductCard
                     key={product.id}
                     product={product}
                     onView={handleViewProduct}
                  />
               ))}
            </div>

            <motion.div
               className="mt-12 text-center"
               initial={{ opacity: 0, y: 20 }}
               whileInView={{ opacity: 1, y: 0 }}
               viewport={{ once: true }}
               transition={{ duration: 0.5 }}
            >
               <Button asChild size="lg" className="group">
                  <Link href="/catalog">
                     View Full Collection{" "}
                     <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
               </Button>
            </motion.div>
         </div>

         {/* <ProductModal
            product={selectedProduct}
            open={modalOpen}
            onOpenChange={setModalOpen}
         /> */}

         <ProductModal
            product={selectedProduct as Product}
            isOpen={modalOpen}
            onClose={() => setModalOpen(false)}
         />
      </section>
   );
}
