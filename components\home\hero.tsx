"use client";

import { motion } from "framer-motion";
import { ArrowRight } from "lucide-react";
import Link from "next/link";

import { Button } from "@/components/ui/button";

export function Hero() {
   return (
      <section className="relative flex min-h-screen items-center justify-center overflow-hidden">
         {/* Background Image with Overlay */}
         <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{
               backgroundImage: "url('/images/home_hero.jpg')",
            }}
         >
            <div className="absolute inset-0 bg-black/60" />
         </div>

         {/* Content */}
         <div className="container relative z-10 pb-12 pt-20 text-white md:py-24">
            <div className="max-w-2xl">
               <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
               >
                  <h1 className="mb-6 text-center font-serif text-5xl font-bold tracking-tight md:text-start lg:text-6xl">
                     Natural Beauty Set in Stone
                  </h1>
                  <p className="text-md mb-8 text-center text-white/90 md:text-start md:text-lg">
                     Discover premium quality natural stone for your home or
                     commercial project. From elegant marble to durable granite,
                     our selection transforms spaces into works of art.
                  </p>
                  <div className="flex flex-col gap-4 sm:flex-row">
                     <Button
                        asChild
                        size="lg"
                        className="w-fit self-center text-base"
                     >
                        <Link href="/catalog">Explore Our Collection</Link>
                     </Button>
                     <Button
                        asChild
                        size="lg"
                        className="group w-fit self-center border-white bg-white text-base text-black hover:bg-white/80"
                     >
                        <Link
                           href="/contact"
                           className="flex items-center gap-2"
                        >
                           Contact Us
                           <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-0.5" />
                        </Link>
                     </Button>
                  </div>
               </motion.div>

               <motion.div
                  className="mt-8 max-w-xl rounded-lg border border-white/10 bg-black/30 px-6 py-4 backdrop-blur-sm"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
               >
                  <p className="font-serif text-lg italic">
                     &quot;Our commitment to quality and craftsmanship ensures
                     that each stone tells its own unique story in your
                     space.&quot;
                  </p>
               </motion.div>
            </div>
         </div>
      </section>
   );
}
