export type Product = {
  id: string;
  name: string;
  description: string;
  shortDescription: string;
  image: string;
  category: ProductCategory;
  price: number | null;
  priceUnit: string | null;
  colors: string[];
  sizes: string[];
  featured: boolean;
  finishes: string[];
};

export type ProductCategory = 
  | "marble" 
  | "granite" 
  | "limestone" 
  | "travertine" 
  | "quartzite" 
  | "slate"
  | "sandstone";

export type FAQItem = {
  question: string;
  answer: string;
};

export type ContactFormValues = {
  name: string;
  email: string;
  phone?: string;
  message: string;
  subject?: string;
};

export type SocialLink = {
  name: string;
  url: string;
  icon: string;
};

export type NavigationLink = {
  name: string;
  href: string;
};

export type FooterLink = {
  title: string;
  links: {
    name: string;
    href: string;
  }[];
};