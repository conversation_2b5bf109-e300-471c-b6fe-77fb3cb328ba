"use client";

import { SectionHeader } from "@/components/ui/section-header";
import { motion } from "framer-motion";
import Image from "next/image";

export function About() {
   const features = [
      {
         title: "Premium Selection",
         description:
            "We source the finest Nigerian granite, formed over millions of years in ancient hills.",
      },
      {
         title: "Natural Excellence",
         description:
            "Pure, untouched stone with no chemicals or artificial enhancements.",
      },
   ];

   return (
      <section id="about" className="py-16 md:py-24">
         <div className="container">
            <div className="grid items-center gap-12 md:grid-cols-2 md:gap-16">
               <motion.div
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true, margin: "-100px" }}
                  transition={{ duration: 0.6 }}
               >
                  <SectionHeader
                     title="The True Spirit of Nigerian Stone"
                     className="md:text-left"
                  />

                  <motion.p
                     className="mt-6 text-muted-foreground"
                     initial={{ opacity: 0 }}
                     whileInView={{ opacity: 1 }}
                     viewport={{ once: true, margin: "-100px" }}
                     transition={{ duration: 0.6, delay: 0.2 }}
                  >
                     At Tvine Stones we bring the true spirit of nature to your
                     space with the finest Nigerian granite, sourced from the
                     ancient hills of Naija. Formed over millions of years, our
                     granite embodies strength, durability, and timeless beauty.
                  </motion.p>

                  <motion.p
                     className="mt-6 text-muted-foreground"
                     initial={{ opacity: 0 }}
                     whileInView={{ opacity: 1 }}
                     viewport={{ once: true, margin: "-100px" }}
                     transition={{ duration: 0.6, delay: 0.2 }}
                  >
                     Each slab tells a story of earth’s resilience, of natural
                     artistry, and of a material that stands the test of time.
                     With no chemicals, no artificial enhancements, just pure,
                     untouched excellence, our granite is the perfect choice for
                     homes, businesses, and architectural masterpieces.
                  </motion.p>

                  <motion.p
                     className="mt-6 text-muted-foreground"
                     initial={{ opacity: 0 }}
                     whileInView={{ opacity: 1 }}
                     viewport={{ once: true, margin: "-100px" }}
                     transition={{ duration: 0.6, delay: 0.2 }}
                  >
                     Whether you seek the rich hues of Abuja Brown, the elegance
                     of Supare Ivory White, or the bold statement of Kano Red,
                     our collection offers unmatched quality.
                  </motion.p>

                  <div className="mt-8 grid gap-6 sm:grid-cols-2">
                     {features.map((feature, index) => (
                        <motion.div
                           key={feature.title}
                           className="rounded-lg bg-background p-4 shadow-sm"
                           initial={{ opacity: 0, y: 20 }}
                           whileInView={{ opacity: 1, y: 0 }}
                           viewport={{ once: true, margin: "-100px" }}
                           transition={{ duration: 0.5, delay: 0.1 * index }}
                        >
                           <h3 className="mb-2 font-serif text-lg font-medium">
                              {feature.title}
                           </h3>
                           <p className="text-sm text-muted-foreground">
                              {feature.description}
                           </p>
                        </motion.div>
                     ))}
                  </div>
               </motion.div>

               <motion.div
                  className="relative h-[500px] overflow-hidden rounded-lg shadow-xl"
                  initial={{ opacity: 0, x: 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true, margin: "-100px" }}
                  transition={{ duration: 0.6 }}
               >
                  <Image
                     src="/images/img-1.jpg"
                     alt="Stone craftsmen at work"
                     fill
                     className="object-cover"
                  />
                  <div className="absolute inset-0 flex flex-col justify-end bg-gradient-to-t from-black/60 to-transparent p-6">
                     <p className="font-serif text-lg italic text-white">
                        &quot;Every stone has a story to tell. We help you tell
                        yours.&quot;
                     </p>
                     {/* <p className="mt-2 text-sm text-white/80">
                        - Michael Tvine, Founder
                     </p> */}
                  </div>
               </motion.div>
            </div>
         </div>
      </section>
   );
}
