import { z } from "zod";

export const contactFormSchema = z.object({
   name: z.string().min(2, {
      message: "Name must be at least 2 characters.",
   }),
   email: z.string().email({
      message: "Please enter a valid email address.",
   }),
   phone: z.string().optional(),
   subject: z.string().optional(),
   message: z.string().min(10, {
      message: "Message must be at least 10 characters.",
   }),
});

export const customRequestFormSchema = z.object({
   name: z.string().min(2, {
      message: "Name must be at least 2 characters.",
   }),
   email: z.string().email({
      message: "Please enter a valid email address.",
   }),
   phone: z.string().optional(),
   productType: z.string().min(1, {
      message: "Please select a product type.",
   }),
   dimensions: z.string().optional(),
   additionalInfo: z.string().optional(),
});
