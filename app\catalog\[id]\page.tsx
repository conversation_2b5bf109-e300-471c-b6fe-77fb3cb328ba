"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { motion } from "framer-motion";
import { notFound, useParams } from "next/navigation";
import { ChevronLeft } from "lucide-react";

import { PRODUCTS } from "@/constants";
import { Product } from "@/types";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { SectionHeader } from "@/components/ui/section-header";
import { ProductCard } from "@/components/ui/product-card";

export default function ProductDetailPage() {
  const params = useParams();
  const [product, setProduct] = useState<Product | null>(null);
  const [relatedProducts, setRelatedProducts] = useState<Product[]>([]);
  
  useEffect(() => {
    const foundProduct = PRODUCTS.find((p) => p.id === params.id);
    
    if (!foundProduct) {
      notFound();
    }
    
    setProduct(foundProduct);
    
    // Find related products in the same category
    if (foundProduct) {
      const related = PRODUCTS.filter(
        (p) => p.category === foundProduct.category && p.id !== foundProduct.id
      ).slice(0, 4);
      
      setRelatedProducts(related);
    }
  }, [params.id]);
  
  if (!product) {
    return null; // Loading state (will redirect to 404 in useEffect if not found)
  }
  
  return (
    <>
      <section className="pt-32 pb-16">
        <div className="container">
          <div className="mb-8">
            <Button variant="ghost" asChild className="mb-6 pl-0">
              <Link href="/catalog" className="flex items-center gap-2">
                <ChevronLeft className="h-4 w-4" />
                Back to Catalog
              </Link>
            </Button>
            
            <div className="grid md:grid-cols-2 gap-8 lg:gap-12">
              <motion.div
                className="relative aspect-square rounded-lg overflow-hidden"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Image
                  src={product.image}
                  alt={product.name}
                  fill
                  className="object-cover"
                />
              </motion.div>
              
              <motion.div
                className="space-y-6"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div>
                  <Badge variant="outline" className="mb-3 capitalize">
                    {product.category}
                  </Badge>
                  <h1 className="font-serif text-3xl md:text-4xl font-bold mb-2">
                    {product.name}
                  </h1>
                  <p className="text-lg text-muted-foreground">
                    {product.shortDescription}
                  </p>
                </div>
                
                {product.price && (
                  <div className="bg-muted/50 p-4 rounded-lg">
                    <div className="flex items-baseline gap-2">
                      <span className="text-2xl font-bold">${product.price}</span>
                      <span className="text-muted-foreground">{product.priceUnit}</span>
                    </div>
                  </div>
                )}
                
                <div className="space-y-4">
                  <h3 className="font-medium text-lg">Description</h3>
                  <p className="text-muted-foreground">{product.description}</p>
                </div>
                
                <Separator />
                
                <div className="grid grid-cols-2 gap-y-4">
                  <div>
                    <h3 className="font-medium mb-2">Available Colors</h3>
                    <div className="flex flex-wrap gap-2">
                      {product.colors.map((color) => (
                        <Badge key={color} variant="secondary">
                          {color}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="font-medium mb-2">Available Sizes</h3>
                    <div className="flex flex-wrap gap-2">
                      {product.sizes.map((size) => (
                        <Badge key={size} variant="outline">
                          {size}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <div className="col-span-2">
                    <h3 className="font-medium mb-2">Available Finishes</h3>
                    <div className="flex flex-wrap gap-2">
                      {product.finishes.map((finish) => (
                        <Badge key={finish} variant="secondary">
                          {finish}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
                
                <div className="pt-4">
                  <Button
                    size="lg"
                    asChild
                    className="w-full sm:w-auto"
                  >
                    <Link href={`/contact?product=${product.id}`}>
                      Request Quote
                    </Link>
                  </Button>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>
      
      {relatedProducts.length > 0 && (
        <section className="py-16 bg-muted/30">
          <div className="container">
            <SectionHeader
              title="You May Also Like"
              subtitle="Explore similar products in our collection"
              centered
            />
            
            <div className="mt-10 grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {relatedProducts.map((relatedProduct) => (
                <ProductCard
                  key={relatedProduct.id}
                  product={relatedProduct}
                />
              ))}
            </div>
          </div>
        </section>
      )}
    </>
  );
}