import Image from "next/image";
import { useState } from "react";

const nigerianGranites = [
   {
      id: "upare-ivory-white",
      name: "Upare Ivory White",
      location: "Upare Quarries",
      image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/img-5.jpg-Sy9g2Pq84cUAM2CFFB0qgLY2IBio4A.png",
      description:
         "Elegant white granite with subtle patterns, perfect for luxury applications",
   },
   {
      id: "champagne",
      name: "Champagne Granite",
      location: "Nigerian Highlands",
      image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/img-4.jpg-E3qLtTGzZ1XBE3tkZj8stHR10TEh8t.jpeg",
      description: "Warm champagne tones with intricate natural patterns",
   },
   {
      id: "abuja-brown",
      name: "Abuja Brown",
      location: "Abuja Region",
      image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/img-3.jpg-byoRNqaSEWNETtHY5bcR57nbLlnzW2.png",
      description: "Rich brown granite with natural variations and warmth",
   },
   {
      id: "jos-dark-green",
      name: "Jos Dark Green",
      location: "Jos Plateau",
      image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/img-1.jpg-Ri0AsslPB4n4wiwTKgykMKp8jHBI7T.png",
      description: "Distinctive dark green with unique mineral compositions",
   },
   {
      id: "supare-gold",
      name: "Supare Gold",
      location: "Supare Quarries",
      image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/img-5.jpg-Sy9g2Pq84cUAM2CFFB0qgLY2IBio4A.png",
      description: "Stunning gold-toned granite with natural flecks",
   },
   {
      id: "african-multicolour",
      name: "African Multicolour",
      location: "Various Regions",
      image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/img-4.jpg-E3qLtTGzZ1XBE3tkZj8stHR10TEh8t.jpeg",
      description: "Vibrant multicolored granite with spectrum of natural hues",
   },
   {
      id: "nigeria-red",
      name: "Nigeria Red",
      location: "Northern Nigeria",
      image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/img-3.jpg-byoRNqaSEWNETtHY5bcR57nbLlnzW2.png",
      description: "Bold red granite making a striking statement",
   },
   {
      id: "kaduna",
      name: "Kaduna Granite",
      location: "Kaduna State",
      image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/img-1.jpg-Ri0AsslPB4n4wiwTKgykMKp8jHBI7T.png",
      description: "Classic Nigerian granite with exceptional durability",
   },
   {
      id: "mpape-green",
      name: "Mpape Green",
      location: "Mpape Hills",
      image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/img-5.jpg-Sy9g2Pq84cUAM2CFFB0qgLY2IBio4A.png",
      description: "Unique green granite with distinctive patterns",
   },
   {
      id: "kano-red",
      name: "Kano Red",
      location: "Kano Region",
      image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/img-3.jpg-byoRNqaSEWNETtHY5bcR57nbLlnzW2.png",
      description: "Bold red granite perfect for statement installations",
   },
];

export default function GraniteShowcase() {
   const [hoveredGranite, setHoveredGranite] = useState<string | null>(null);

   return (
      <div className="mb-16">
         <div className="mb-12 text-center">
            <h3 className="mb-4 text-3xl font-bold">
               Our Nigerian Granite Collection
            </h3>
            <p className="mx-auto max-w-2xl text-lg text-stone-600">
               Explore our carefully curated selection of premium Nigerian
               granite, each with its own unique character and beauty.
            </p>
         </div>

         <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {nigerianGranites.map((granite) => (
               <div
                  key={granite.id}
                  className="group relative cursor-pointer overflow-hidden rounded-xl border border-stone-200 bg-white shadow-sm transition-all duration-300 hover:scale-[1.02] hover:shadow-xl"
                  onMouseEnter={() => setHoveredGranite(granite.id)}
                  onMouseLeave={() => setHoveredGranite(null)}
               >
                  <div className="relative h-64 overflow-hidden">
                     <Image
                        src={granite.image || "/placeholder.svg"}
                        alt={`${granite.name} - Premium Nigerian granite from ${granite.location}`}
                        fill
                        className="object-cover transition-transform duration-500 group-hover:scale-110"
                     />
                     <div
                        className={`absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent transition-opacity duration-300 ${
                           hoveredGranite === granite.id
                              ? "opacity-100"
                              : "opacity-0"
                        }`}
                     />
                     <div
                        className={`absolute bottom-4 left-4 right-4 transform text-white transition-all duration-300 ${
                           hoveredGranite === granite.id
                              ? "translate-y-0 opacity-100"
                              : "translate-y-4 opacity-0"
                        }`}
                     >
                        <p className="mb-1 text-sm font-medium">
                           {granite.location}
                        </p>
                        <p className="line-clamp-2 text-xs opacity-90">
                           {granite.description}
                        </p>
                     </div>
                  </div>
                  <div className="p-4">
                     <h4 className="mb-1 text-lg font-bold">{granite.name}</h4>
                     <p className="text-sm text-stone-500">Nigerian Granite</p>
                  </div>
               </div>
            ))}
         </div>
      </div>
   );
}
