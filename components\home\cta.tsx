import { <PERSON><PERSON><PERSON> } from "lucide-react";
import Link from "next/link";
import { <PERSON><PERSON> } from "../ui/button";

export default function CTA() {
   return (
      <div className="container mx-auto mt-14 px-4 text-center">
         <div className="rounded-2xl bg-gradient-to-r from-stone-100 to-stone-50 p-8 md:p-12">
            <h3 className="mb-4 text-3xl font-bold">
               Ready to Transform Your Space?
            </h3>
            <p className="mx-auto mb-8 max-w-2xl text-lg text-stone-600">
               Discover the perfect Nigerian granite for your project. Our
               experts are ready to help you find the ideal stone that matches
               your vision and requirements.
            </p>
            <div className="flex flex-col justify-center gap-4 sm:flex-row">
               <Button
                  asChild
                  size="lg"
                  className="bg-stone-800 hover:bg-stone-700"
               >
                  <Link href="/catalog" className="flex items-center">
                     View Our Granite Collection
                     <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
               </Button>
               <Button
                  asChild
                  variant="outline"
                  size="lg"
                  className="border-stone-300 hover:bg-stone-50"
               >
                  <Link href="/contact">Get a Quote for Your Project</Link>
               </Button>
            </div>
         </div>
      </div>
   );
}
