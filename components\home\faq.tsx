"use client";

import { motion } from "framer-motion";

import {
   Accordion,
   AccordionContent,
   AccordionItem,
   AccordionTrigger,
} from "@/components/ui/accordion";
import { SectionHeader } from "@/components/ui/section-header";
import { FAQ_ITEMS } from "@/constants";

export function FAQ() {
   return (
      <section id="faq" className="py-16 md:py-24">
         <div className="container">
            <SectionHeader
               title="Frequently Asked Questions"
               subtitle="Find answers to common questions about our products and services."
               centered
            />

            <motion.div
               className="mx-auto mt-12 max-w-3xl"
               initial={{ opacity: 0, y: 20 }}
               whileInView={{ opacity: 1, y: 0 }}
               viewport={{ once: true, margin: "-100px" }}
               transition={{ duration: 0.5 }}
            >
               <Accordion type="single" collapsible className="w-full">
                  {FAQ_ITEMS.map((faq, index) => (
                     <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        viewport={{ once: true, margin: "-100px" }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                     >
                        <AccordionItem key={index} value={`item-${index}`}>
                           <AccordionTrigger className="text-left hover:no-underline">
                              {faq.question}
                           </AccordionTrigger>
                           <AccordionContent>
                              {faq.answer
                                 .split("\n\n")
                                 .map((paragraph, idx) => (
                                    <p key={idx} className="mb-2 last:mb-0">
                                       {paragraph}
                                    </p>
                                 ))}
                           </AccordionContent>
                        </AccordionItem>
                     </motion.div>
                  ))}
               </Accordion>
            </motion.div>
         </div>
      </section>
   );
}
