"use client";

import { CatalogHeader } from "@/components/catalog/catalog-header";
import CatalogHero from "@/components/catalog/catalog-hero";
import { CustomRequest } from "@/components/catalog/custom-request";
import { ContactPreview } from "@/components/home/<USER>";
import { ProductCard } from "@/components/ui/product-card";
import ProductModal from "@/components/ui/product-modal-2";
// import { ProductModal } from "@/components/ui/product-modal";
import { PRODUCTS } from "@/constants";
import { Product, ProductCategory } from "@/types";
import { useSearchParams } from "next/navigation";
import { useCallback, useEffect, useState } from "react";

export default function CatalogPage() {
   const searchParams = useSearchParams();
   const [selectedCategory, setSelectedCategory] =
      useState<ProductCategory | null>(null);
   const [searchQuery, setSearchQuery] = useState("");
   const [filteredProducts, setFilteredProducts] =
      useState<Product[]>(PRODUCTS);
   const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
   const [modalOpen, setModalOpen] = useState(false);

   const categories = Array.from(
      new Set(PRODUCTS.map((product) => product.category)),
   ) as ProductCategory[];

   const filterProducts = useCallback(() => {
      let result = [...PRODUCTS];

      if (selectedCategory) {
         result = result.filter(
            (product) => product.category === selectedCategory,
         );
      }

      if (searchQuery) {
         const query = searchQuery.toLowerCase();
         result = result.filter(
            (product) =>
               product.name.toLowerCase().includes(query) ||
               product.description.toLowerCase().includes(query) ||
               product.category.toLowerCase().includes(query),
         );
      }

      setFilteredProducts(result);
   }, [selectedCategory, searchQuery]);

   useEffect(() => {
      filterProducts();
   }, [filterProducts]);

   useEffect(() => {
      const categoryParam = searchParams.get(
         "category",
      ) as ProductCategory | null;
      if (categoryParam && categories.includes(categoryParam)) {
         setSelectedCategory(categoryParam);
      }
   }, [searchParams, categories]);

   const handleViewProduct = (product: Product) => {
      setSelectedProduct(product);
      setModalOpen(true);
   };

   return (
      <>
         <CatalogHero />
         <section className="pb-16 pt-12 md:pb-24">
            <div className="container">
               <div>
                  <CatalogHeader
                     categories={categories}
                     selectedCategory={selectedCategory}
                     searchQuery={searchQuery}
                     onCategoryChange={setSelectedCategory}
                     onSearchChange={setSearchQuery}
                  />

                  {filteredProducts.length > 0 ? (
                     <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                        {filteredProducts.map((product) => (
                           <ProductCard
                              key={product.id}
                              product={product}
                              onView={handleViewProduct}
                           />
                        ))}
                     </div>
                  ) : (
                     <div className="py-16 text-center">
                        <h3 className="mb-2 text-xl font-medium">
                           No products found
                        </h3>
                        <p className="text-muted-foreground">
                           Try adjusting your search or filter to find what
                           you&apos;re looking for.
                        </p>
                     </div>
                  )}
               </div>
            </div>
         </section>

         <CustomRequest />
         <ContactPreview />

         {/* <ProductModal
            product={selectedProduct}
            open={modalOpen}
            onOpenChange={setModalOpen}
         /> */}

         <ProductModal
            product={selectedProduct as Product}
            isOpen={modalOpen}
            onClose={() => setModalOpen(false)}
         />
      </>
   );
}
