"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";
import { Check, Send } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   Form,
   FormControl,
   FormField,
   FormItem,
   FormLabel,
   FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { contactFormSchema } from "@/schemas";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "../ui/select";

type FormValues = z.infer<typeof contactFormSchema>;

interface ContactFormProps {
   productId?: string;
}

export function ContactForm({ productId }: ContactFormProps) {
   const [isSubmitted, setIsSubmitted] = useState(false);

   const form = useForm<FormValues>({
      resolver: zodResolver(contactFormSchema),
      defaultValues: {
         name: "",
         email: "",
         phone: "",
         subject: productId ? `Quote Request for Product #${productId}` : "",
         message: "",
      },
   });

   function onSubmit(values: FormValues) {
      console.log(values);
      setIsSubmitted(true);
      // In a real app, you would send this data to your server
   }

   return (
      <motion.div
         className="self-center rounded-xl bg-white p-6 shadow-md md:p-8"
         initial={{ opacity: 0, y: 20 }}
         animate={{ opacity: 1, y: 0 }}
         transition={{ duration: 0.5 }}
      >
         {isSubmitted ? (
            <div className="py-8 text-center">
               <div className="mb-4 inline-flex rounded-full bg-green-100 p-3 text-green-800">
                  <Check className="h-6 w-6" />
               </div>
               <h3 className="mb-2 font-serif text-2xl font-medium">
                  Message Sent
               </h3>
               <p className="mb-6 text-muted-foreground">
                  Thank you for contacting us. We will get back to you as soon
                  as possible.
               </p>
               <Button onClick={() => setIsSubmitted(false)}>
                  Send Another Message
               </Button>
            </div>
         ) : (
            <>
               <h2 className="mb-6 font-serif text-2xl font-bold">
                  Send Us a Message
               </h2>
               <Form {...form}>
                  <form
                     onSubmit={form.handleSubmit(onSubmit)}
                     className="space-y-6"
                  >
                     <div className="grid gap-6 sm:grid-cols-2">
                        <FormField
                           control={form.control}
                           name="name"
                           render={({ field }) => (
                              <FormItem>
                                 <FormLabel className="ml-1">Name</FormLabel>
                                 <FormControl>
                                    <Input placeholder="Your name" {...field} />
                                 </FormControl>
                                 <FormMessage />
                              </FormItem>
                           )}
                        />
                        <FormField
                           control={form.control}
                           name="email"
                           render={({ field }) => (
                              <FormItem>
                                 <FormLabel className="ml-1">Email</FormLabel>
                                 <FormControl>
                                    <Input
                                       placeholder="Your email"
                                       type="email"
                                       {...field}
                                    />
                                 </FormControl>
                                 <FormMessage />
                              </FormItem>
                           )}
                        />
                     </div>

                     <div className="grid gap-6 sm:grid-cols-2">
                        <FormField
                           control={form.control}
                           name="phone"
                           render={({ field }) => (
                              <FormItem>
                                 <FormLabel className="ml-1">
                                    Phone (Optional)
                                 </FormLabel>
                                 <FormControl>
                                    <Input
                                       placeholder="Your phone number"
                                       {...field}
                                       type="number"
                                    />
                                 </FormControl>
                                 <FormMessage />
                              </FormItem>
                           )}
                        />
                        <FormField
                           control={form.control}
                           name="subject"
                           render={({ field }) => (
                              <FormItem>
                                 <FormLabel className="ml-1">Subject</FormLabel>
                                 <Select
                                    onValueChange={field.onChange}
                                    defaultValue={field.value}
                                 >
                                    <FormControl>
                                       <SelectTrigger>
                                          <SelectValue placeholder="Select a subject" />
                                       </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                       <SelectItem value="general">
                                          General Inquiry
                                       </SelectItem>
                                       <SelectItem value="quote">
                                          Request a Quote
                                       </SelectItem>
                                       <SelectItem value="custom">
                                          Custom Order
                                       </SelectItem>
                                       <SelectItem value="support">
                                          Support
                                       </SelectItem>
                                       <SelectItem value="others">
                                          Others
                                       </SelectItem>
                                    </SelectContent>
                                 </Select>
                                 <FormMessage />
                              </FormItem>
                           )}
                        />
                     </div>

                     <FormField
                        control={form.control}
                        name="message"
                        render={({ field }) => (
                           <FormItem>
                              <FormLabel className="ml-1">Message</FormLabel>
                              <FormControl>
                                 <Textarea
                                    placeholder="Tell us about your project or inquiry"
                                    className="min-h-[150px]"
                                    {...field}
                                 />
                              </FormControl>
                              <FormMessage />
                           </FormItem>
                        )}
                     />

                     <Button type="submit" className="w-full">
                        <Send className="mr-2 h-4 w-4" />
                        Send Message
                     </Button>
                  </form>
               </Form>
            </>
         )}
      </motion.div>
   );
}
