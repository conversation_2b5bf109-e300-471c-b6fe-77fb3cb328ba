"use client";

import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import { ArrowRight } from "lucide-react";
import Link from "next/link";

export default function CatalogHero() {
   return (
      <section className="relative flex h-[60vh] min-h-[500px] items-center overflow-hidden py-24 md:py-32">
         {/* Background Image with Overlay */}
         <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{
               backgroundImage: "url('/images/catalog_hero.jpg')",
            }}
         >
            <div className="absolute inset-0 bg-black/60" />
         </div>

         {/* Content */}
         <div className="container relative z-10">
            <motion.div
               className="mx-auto max-w-2xl text-center text-white"
               initial={{ opacity: 0, y: 20 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.8 }}
            >
               <h1 className="mb-6 font-serif text-3xl font-bold md:text-4xl lg:text-5xl">
                  Our Stone Collection
               </h1>
               <p className="text-xl text-white/90">
                  Browse our premium selection of natural stones, each with
                  unique patterns and exceptional quality.
               </p>

               <div className="mt-4 flex flex-col justify-center space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
                  <Button
                     asChild
                     size="lg"
                     className="group mt-2 bg-stone-900 transition-colors hover:bg-stone-800"
                  >
                     <Link href="/contact">
                        Contact Us
                        <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-0.5" />
                     </Link>
                  </Button>
               </div>
            </motion.div>
         </div>
      </section>
   );
}
