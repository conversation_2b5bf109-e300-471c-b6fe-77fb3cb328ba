"use client";

import { motion } from "framer-motion";
import { ArrowRight, Mail, MessageSquare, Phone } from "lucide-react";
import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";
import { SectionHeader } from "@/components/ui/section-header";
import { CONTACT_INFO } from "@/constants";

export function ContactPreview() {
   return (
      <section className="relative overflow-hidden py-16 md:py-24">
         {/* Background Pattern */}
         <div className="absolute inset-0 -z-10 bg-muted/60">
            <div className="absolute inset-0 bg-[radial-gradient(#334155_1px,transparent_1px)] opacity-30 [background-size:20px_20px]"></div>
         </div>

         <div className="container relative z-10">
            <motion.div
               initial={{ opacity: 0, y: 20 }}
               whileInView={{ opacity: 1, y: 0 }}
               viewport={{ once: true }}
               transition={{ duration: 0.5 }}
               className="mx-auto max-w-5xl text-center"
            >
               <SectionHeader
                  title="Get In Touch"
                  subtitle="Contact us for a personalized consultation or request a quote for your project."
                  centered
               />

               <div className="mt-12 grid gap-6 md:grid-cols-3">
                  {/* Phone */}
                  <motion.div
                     initial={{ opacity: 0, x: -20 }}
                     whileInView={{ opacity: 1, x: 0 }}
                     viewport={{ once: true }}
                     transition={{ duration: 0.5, delay: 0.2 }}
                     className="flex flex-col rounded-lg border border-stone-200 bg-white p-6 text-center shadow-sm"
                  >
                     <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-stone-100">
                        <Phone className="h-6 w-6 text-stone-800" />
                     </div>
                     <h3 className="mb-2 text-xl font-medium">Call Us</h3>
                     <p className="mb-4 text-stone-600">
                        Speak directly with our stone experts
                     </p>
                     <Button
                        asChild
                        variant="outline"
                        className="mt-auto w-full"
                     >
                        <Link href={`tel:${CONTACT_INFO.phone}`}>
                           {CONTACT_INFO.phone}
                        </Link>
                     </Button>
                  </motion.div>

                  {/* Email */}
                  <motion.div
                     initial={{ opacity: 0, x: 20 }}
                     whileInView={{ opacity: 1, x: 0 }}
                     viewport={{ once: true }}
                     transition={{ duration: 0.5, delay: 0.3 }}
                     className="flex flex-col rounded-lg border border-stone-200 bg-white p-6 text-center shadow-sm"
                  >
                     <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-stone-100">
                        <Mail className="h-6 w-6 text-stone-800" />
                     </div>
                     <h3 className="mb-2 text-xl font-medium">Email Us</h3>
                     <p className="mb-4 text-stone-600">
                        Send us your inquiries anytime
                     </p>
                     <Button
                        asChild
                        variant="outline"
                        className="mt-auto w-full"
                     >
                        <Link href={`mailto:${CONTACT_INFO.email}`}>
                           {CONTACT_INFO.email}
                        </Link>
                     </Button>
                  </motion.div>

                  {/* Contact Form */}
                  <motion.div
                     initial={{ opacity: 0, x: 20 }}
                     whileInView={{ opacity: 1, x: 0 }}
                     viewport={{ once: true }}
                     transition={{ duration: 0.5, delay: 0.4 }}
                     className="flex flex-col rounded-lg border border-stone-200 bg-white p-6 text-center shadow-sm"
                  >
                     <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-stone-100">
                        <MessageSquare className="h-6 w-6 text-stone-800" />
                     </div>
                     <h3 className="mb-2 text-xl font-medium">Contact Form</h3>
                     <p className="mb-4 text-stone-600">
                        Fill out our contact form for detailed inquiries
                     </p>
                     <Button
                        asChild
                        className="mt-auto w-full bg-stone-800 hover:bg-stone-700"
                     >
                        <Link href="/contact">Contact Us</Link>
                     </Button>
                  </motion.div>
               </div>

               <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                  className="mt-12"
               >
                  <Button asChild size="lg" className="group">
                     <Link href="/catalog">
                        Contact Us
                        <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                     </Link>
                  </Button>
               </motion.div>
            </motion.div>
         </div>
      </section>
   );
}
