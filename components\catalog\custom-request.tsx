"use client";

import { motion } from "framer-motion";
import { ArrowR<PERSON>, SearchIcon } from "lucide-react";

import { Button } from "@/components/ui/button";
import Link from "next/link";

export function CustomRequest() {
   return (
      <section className="bg-stone-50 py-16 md:py-24">
         <div className="container mx-auto px-4">
            <div className="mx-auto max-w-5xl overflow-hidden rounded-lg bg-white shadow-lg">
               <div className="grid grid-cols-1 md:grid-cols-2">
                  {/* Image Column */}
                  <div className="relative h-60 bg-amber-600 md:h-auto">
                     <div
                        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
                        style={{
                           backgroundImage: 'url("/images/img-4.jpg")',
                           opacity: 0.8,
                        }}
                     />
                     <div className="absolute inset-0 flex items-center justify-center">
                        <SearchIcon className="h-24 w-24 text-white opacity-75" />
                     </div>
                  </div>

                  {/* Content Column */}
                  <motion.div
                     initial={{ opacity: 0, x: 20 }}
                     whileInView={{ opacity: 1, x: 0 }}
                     viewport={{ once: true }}
                     transition={{ duration: 0.5 }}
                     className="p-8 md:p-12"
                  >
                     <h3 className="mb-4 font-serif text-2xl font-bold text-stone-800 md:text-3xl">
                        Looking for Something Specific?
                     </h3>
                     <p className="mb-4 text-stone-600">
                        Can&apos;t find what you need in our catalog? We
                        specialize in sourcing unique and rare stone materials
                        to meet your exact specifications.
                     </p>
                     <p className="mb-4 text-stone-600">
                        Contact us with your requirements, and our team will
                        work with you to find the perfect stone for your
                        project.
                     </p>
                     <Button
                        asChild
                        className="bg-amber-600 text-white hover:bg-amber-700"
                     >
                        <Link
                           href="/contact"
                           className="flex items-center gap-2"
                        >
                           Request Custom Order <ArrowRight size={16} />
                        </Link>
                     </Button>
                  </motion.div>
               </div>
            </div>
         </div>
      </section>
   );
}
