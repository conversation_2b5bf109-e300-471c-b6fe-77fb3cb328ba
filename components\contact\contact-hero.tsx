"use client";

import { motion } from "framer-motion";
import { ArrowR<PERSON> } from "lucide-react";
import Link from "next/link";
import { Button } from "../ui/button";

export function ContactHero() {
   return (
      <section className="relative flex h-[60vh] min-h-[500px] items-center overflow-hidden py-24 md:py-32">
         {/* Background Image with Overlay */}
         <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{
               backgroundImage: "url('/images/contact_hero.jpg')",
            }}
         >
            <div className="absolute inset-0 bg-black/60" />
         </div>

         {/* Content */}
         <div className="container relative z-10">
            <motion.div
               className="mx-auto max-w-3xl text-center text-white"
               initial={{ opacity: 0, y: 20 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.8 }}
            >
               <h1 className="mb-6 font-serif text-3xl font-bold md:text-4xl lg:text-5xl">
                  Contact Us
               </h1>
               <p className="text-xl text-white/90">
                  Have questions about our products or need assistance with your
                  project? We&apos;re here to help you find the perfect stone
                  solution.
               </p>
               <div className="mt-4 flex flex-col justify-center space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
                  <Button
                     asChild
                     size="lg"
                     className="group mt-2 bg-stone-900 transition-colors hover:bg-stone-800"
                  >
                     <Link href="/catalog">
                        Browse Collections
                        <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-0.5" />
                     </Link>
                  </Button>
               </div>
            </motion.div>
         </div>
      </section>
   );
}
