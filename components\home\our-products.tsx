"use client";

import { motion } from "framer-motion";
import { ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { Button } from "../ui/button";
import { SectionHeader } from "../ui/section-header";

export default function OurProducts() {
   const fadeIn = {
      hidden: { opacity: 0, y: 20 },
      visible: {
         opacity: 1,
         y: 0,
         transition: { duration: 0.6 },
      },
   };

   const staggerContainer = {
      hidden: { opacity: 0 },
      visible: {
         opacity: 1,
         transition: {
            staggerChildren: 0.2,
         },
      },
   };

   return (
      <section className="py-16 md:py-24">
         <div className="container">
            <motion.div
               className="mx-auto mb-16 max-w-3xl text-center"
               initial="hidden"
               whileInView="visible"
               viewport={{ once: true, margin: "-100px" }}
               variants={fadeIn}
            >
               <SectionHeader
                  title="Our Products"
                  subtitle="Discover our extensive range of premium marble and granite products, carefully selected to enhance the beauty and functionality of your space."
               />
            </motion.div>

            <motion.div
               className="mx-auto grid max-w-6xl gap-8 md:grid-cols-2"
               initial="hidden"
               whileInView="visible"
               viewport={{ once: true, margin: "-100px" }}
               variants={staggerContainer}
            >
               {/* Granite Card */}
               <motion.div
                  variants={fadeIn}
                  className="group relative overflow-hidden rounded-lg"
               >
                  <Link href="/catalog?type=granite">
                     <div className="absolute inset-0 z-10 bg-black/30 transition-colors group-hover:bg-black/40"></div>
                     <Image
                        src="/images/marbles.jpg"
                        alt="Granite samples"
                        width={600}
                        height={400}
                        className="h-[400px] w-full object-cover transition-transform duration-500 group-hover:scale-105"
                     />
                     <div className="absolute inset-0 z-20 flex flex-col justify-end p-6">
                        <h3 className="mb-2 font-serif text-2xl font-bold text-white">
                           Granite Collection
                        </h3>
                        <p className="mb-4 text-white/90">
                           Durable, heat-resistant, and available in a variety
                           of colors and patterns, our granite collection is
                           perfect for kitchen countertops and high-traffic
                           areas.
                        </p>
                        <Button variant="outline" className="font-md w-fit">
                           Explore Granite
                           <ChevronRight className="ml-1 h-4 w-4 transition-transform group-hover:translate-x-1" />
                        </Button>
                     </div>
                  </Link>
               </motion.div>

               {/* Marble Card */}
               <motion.div
                  variants={fadeIn}
                  className="group relative overflow-hidden rounded-lg"
               >
                  <Link href="/catalog?type=marble">
                     <div className="absolute inset-0 z-10 bg-black/30 transition-colors group-hover:bg-black/40"></div>
                     <Image
                        src="/images/granite.jpg"
                        alt="Marble samples"
                        width={600}
                        height={400}
                        className="h-[400px] w-full object-cover transition-transform duration-500 group-hover:scale-105"
                     />
                     <div className="absolute inset-0 z-20 flex flex-col justify-end p-6">
                        <h3 className="mb-2 font-serif text-2xl font-bold text-white">
                           Marble Collection
                        </h3>
                        <p className="mb-4 text-white/90">
                           Elegant and timeless, our marble collection adds
                           sophistication to any space, perfect for bathrooms,
                           fireplaces, and decorative elements.
                        </p>
                        <Button variant="outline" className="font-md w-fit">
                           Explore Marble
                           <ChevronRight className="ml-1 h-4 w-4 transition-transform group-hover:translate-x-1" />
                        </Button>
                     </div>
                  </Link>
               </motion.div>
            </motion.div>
         </div>
      </section>
   );
}
