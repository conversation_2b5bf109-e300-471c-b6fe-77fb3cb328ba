"use client";

import { motion } from "framer-motion";
import { <PERSON><PERSON>, Phone } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";

import { Button } from "@/components/ui/button";
import {
   Sheet,
   Sheet<PERSON>ontent,
   <PERSON><PERSON><PERSON><PERSON><PERSON>,
   SheetTrigger,
} from "@/components/ui/sheet";
import { NAVIGATION_LINKS } from "@/constants";
import { cn } from "@/lib/utils";

export function Navbar() {
   const [isScrolled, setIsScrolled] = useState(false);

   useEffect(() => {
      const handleScroll = () => {
         setIsScrolled(window.scrollY > 10);
      };

      window.addEventListener("scroll", handleScroll);
      return () => window.removeEventListener("scroll", handleScroll);
   }, []);

   return (
      <motion.header
         className={cn(
            "fixed top-0 left-0 right-0 z-50 transition-all duration-300",
            isScrolled ? "bg-background/95 backdrop-blur-sm" : "bg-transparent"
         )}
         initial={{ y: -100 }}
         animate={{ y: 0 }}
         transition={{ duration: 0.5 }}
      >
         <div className="container flex items-center justify-between h-16 md:h-20">
            <Link
               href="/"
               className="font-serif text-2xl font-bold tracking-tight"
            >
               <span
                  className={cn(
                     "transition-colors duration-300",
                     isScrolled ? "text-foreground" : "text-primary-foreground"
                  )}
               >
                  Tvine Stones
               </span>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
               {NAVIGATION_LINKS.map((link) => (
                  <Link
                     key={link.href}
                     href={link.href}
                     className={cn(
                        "text-md font-medium transition-colors hover:text-primary-foreground/80",
                        isScrolled
                           ? "text-foreground"
                           : "text-primary-foreground"
                     )}
                  >
                     {link.name}
                  </Link>
               ))}
               <Button
                  asChild
                  size={"sm"}
                  variant={"default"}
                  className={
                     !isScrolled
                        ? "text-primary-foreground border-primary-foreground bg-white text-black hover:text-primary hover:bg-primary-foreground/80"
                        : ""
                  }
               >
                  <Link href="tel:+1234567890">
                     <Phone className="mr-2 h-4 w-4" />
                     Get a Quote
                  </Link>
               </Button>
            </nav>

            {/* Mobile Navigation */}
            <Sheet>
               <SheetTrigger asChild>
                  <Button
                     variant="ghost"
                     size="icon"
                     className={cn(
                        "md:hidden",
                        isScrolled
                           ? "text-foreground"
                           : "text-primary-foreground"
                     )}
                  >
                     <Menu className="h-6 w-6" />
                     <span className="sr-only">Toggle menu</span>
                  </Button>
               </SheetTrigger>
               <SheetContent side="right" className="w-[250px] sm:w-[300px]">
                  <SheetTitle className="sr-only">Menu</SheetTitle>
                  <div className="flex flex-col h-full">
                     <div className="flex items-center justify-between py-4">
                        <Link href="/" className="font-serif text-xl font-bold">
                           Tvine Stones
                        </Link>
                     </div>
                     <nav className="flex flex-col space-y-6 py-6">
                        {NAVIGATION_LINKS.map((link) => (
                           <Link
                              key={link.href}
                              href={link.href}
                              className="text-base font-medium transition-colors hover:text-primary"
                           >
                              {link.name}
                           </Link>
                        ))}
                     </nav>
                     <div className="mt-auto pt-6">
                        <Button asChild className="w-full">
                           <Link href="/contact">Get a Quote</Link>
                        </Button>
                     </div>
                  </div>
               </SheetContent>
            </Sheet>
         </div>
      </motion.header>
   );
}
