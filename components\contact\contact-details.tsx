"use client";

import { CONTACT_INFO } from "@/constants";
import { motion } from "framer-motion";
import { Mail, MapPin, MessageSquare, Phone } from "lucide-react";
import Link from "next/link";

export function ContactDetails() {
   return (
      <motion.div
         className="rounded-lg p-6 md:p-8"
         initial={{ opacity: 0, y: 20 }}
         animate={{ opacity: 1, y: 0 }}
         transition={{ duration: 0.5, delay: 0.2 }}
      >
         <h2 className="mb-4 font-serif text-3xl font-bold">Get In Touch </h2>
         <p className="mb-8 text-muted-foreground">
            Whether you have questions about our products, need a custom quote,
            or want to discuss your project requirements, our team is here to
            help. Fill out the form or use one of our contact methods below.
         </p>
         <div className="space-y-6">
            <div className="flex items-center space-x-4">
               <div className="rounded-full bg-[#e8e8e7] p-3">
                  <Phone className="h-5 w-5" />
               </div>
               <div>
                  <h3 className="text-lg font-semibold">Phone</h3>
                  <p className="mb-1 text-sm text-muted-foreground">
                     Call us directly at:
                  </p>
                  <Link
                     href={`tel:${CONTACT_INFO.phone}`}
                     className="hover:underline"
                  >
                     <p className="text-sm font-semibold text-muted-foreground">
                        {CONTACT_INFO.phone}
                     </p>
                  </Link>
               </div>
            </div>

            <div className="flex items-center space-x-4">
               <div className="rounded-full bg-[#e8e8e7] p-3">
                  <Mail className="h-5 w-5" />
               </div>
               <div>
                  <h3 className="text-lg font-semibold">Email</h3>
                  <p className="mb-1 text-sm text-muted-foreground">
                     Send us an email at:
                  </p>
                  <Link
                     href={`mailto:${CONTACT_INFO.email}`}
                     className="hover:underline"
                  >
                     <p className="text-sm font-semibold text-muted-foreground">
                        {CONTACT_INFO.email}
                     </p>
                  </Link>
               </div>
            </div>

            <div className="flex items-center space-x-4">
               <div className="rounded-full bg-[#e8e8e7] p-3">
                  <MapPin className="h-5 w-5" />
               </div>
               <div>
                  <h3 className="text-lg font-semibold">Address</h3>
                  <p className="mb-1 text-sm text-muted-foreground">
                     Visit our showroom at:
                  </p>
                  <p className="text-sm font-semibold text-muted-foreground">
                     {CONTACT_INFO.address}
                  </p>
               </div>
            </div>

            <div className="flex items-center space-x-4">
               <div className="rounded-full bg-[#e8e8e7] p-3">
                  <MessageSquare className="h-5 w-5" />
               </div>
               <div>
                  <h3 className="text-lg font-semibold">WhatsApp</h3>
                  <p className="mb-1 text-sm text-muted-foreground">
                     Message us on WhatsApp at:
                  </p>
                  <Link
                     href={`https://wa.me/${CONTACT_INFO.phone}`}
                     className="hover:underline"
                  >
                     <p className="text-sm font-semibold text-muted-foreground">
                        {CONTACT_INFO.phone}
                     </p>
                  </Link>
               </div>
            </div>
         </div>
      </motion.div>
   );
}
