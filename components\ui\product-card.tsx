"use client";

import { cn } from "@/lib/utils";
import { Product } from "@/types";
import { motion } from "framer-motion";
import { Maximize2 } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

interface ProductCardProps {
   product: Product;
   onView?: (product: Product) => void;
   className?: string;
}

export function ProductCard({ product, onView, className }: ProductCardProps) {
   const [isHovered, setIsHovered] = useState(false);

   return (
      <motion.div
         initial={{ opacity: 0, y: 20 }}
         whileInView={{ opacity: 1, y: 0 }}
         viewport={{ once: true, margin: "-100px" }}
         transition={{ duration: 0.5 }}
         className={cn(
            className,
            "cursor-pointer overflow-hidden rounded-lg border border-stone-200 bg-white shadow-sm",
         )}
         onHoverStart={() => setIsHovered(true)}
         onHoverEnd={() => setIsHovered(false)}
         onClick={() => onView && onView(product)}
      >
         <div className="relative aspect-[16/9] overflow-hidden">
            <Image
               src={product.image}
               alt={product.name}
               fill
               className="object-cover transition-transform duration-300"
               style={{ transform: isHovered ? "scale(1.05)" : "scale(1)" }}
            />
            <div
               className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-20 opacity-0 transition-opacity duration-300"
               style={{ opacity: isHovered ? 1 : 0 }}
            >
               <div className="rounded-full bg-white p-2">
                  <Maximize2 className="h-5 w-5 text-stone-800" />
               </div>
            </div>
         </div>
         <div className="p-4">
            <h3 className="text-lg font-medium">{product.name}</h3>
            <p className="text-sm capitalize text-stone-500">
               {product.category}
            </p>
         </div>
      </motion.div>
   );
}
