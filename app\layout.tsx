import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";

import { Footer } from "@/components/layout/footer";
import { Navbar } from "@/components/layout/navbar";
import { inter, notoSerif } from "./fonts";

export const metadata: Metadata = {
   title: "Tvine Stones | Premium Natural Stone Solutions",
   description:
      "Discover premium quality natural stone for your home or commercial project. From elegant marble to durable granite, our selection transforms spaces into works of art.",
};

export default function RootLayout({
   children,
}: {
   children: React.ReactNode;
}) {
   return (
      <html lang="en">
         <body className={`${inter.className} ${notoSerif.variable} font-sans`}>
            <Navbar />
            <main>{children}</main>
            <Footer />
         </body>
      </html>
   );
}
