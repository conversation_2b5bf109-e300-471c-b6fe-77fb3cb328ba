"use client";

import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

import { ContactDetails } from "@/components/contact/contact-details";
import { ContactForm } from "@/components/contact/contact-form";
import { ContactHero } from "@/components/contact/contact-hero";
import { PRODUCTS } from "@/constants";
import { Product } from "@/types";

export default function ContactPage() {
   const searchParams = useSearchParams();
   const [productId, setProductId] = useState<string | undefined>(undefined);
   const [product, setProduct] = useState<Product | null>(null);

   useEffect(() => {
      const productParam = searchParams.get("product");

      if (productParam) {
         setProductId(productParam);

         const foundProduct = PRODUCTS.find((p) => p.id === productParam);
         if (foundProduct) {
            setProduct(foundProduct);
         }
      }
   }, [searchParams]);

   return (
      <>
         <ContactHero />

         <section className="bg-muted/60 py-16 md:py-24">
            <div className="container">
               {product && (
                  <div className="mb-10 rounded-lg bg-muted/30 px-4 md:px-6">
                     <p className="text-center">
                        You&apos;re requesting information about
                        <strong>{product.name}</strong>. Please fill out the
                        form below and we&apos;ll get back to you with pricing
                        and availability.
                     </p>
                  </div>
               )}

               <div className="grid gap-8 md:gap-12 lg:grid-cols-2">
                  <ContactDetails />
                  <ContactForm productId={productId} />
               </div>
            </div>
         </section>
      </>
   );
}
