"use client";

import { motion } from "framer-motion";
// import { useState } from "react";
// import { Check, ChevronsUpDown, Search } from "lucide-react";
import { Search } from "lucide-react";

// import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuGroup,
//   DropdownMenuItem,
//   DropdownMenuTrigger,
// } from "@/components/ui/dropdown-menu";
import { ProductCategory } from "@/types";

interface CatalogHeaderProps {
   categories: ProductCategory[];
   selectedCategory: ProductCategory | null;
   searchQuery: string;
   onCategoryChange: (category: ProductCategory | null) => void;
   onSearchChange: (query: string) => void;
}

export function CatalogHeader({
   //   categories,
   //   selectedCategory,
   searchQuery,
   //   onCategoryChange,
   onSearchChange,
}: CatalogHeaderProps) {
   //   const [isOpen, setIsOpen] = useState(false);

   return (
      <motion.div
         // className="mb-8 flex flex-col items-center justify-between gap-4 md:flex-row"
         className="mb-8 flex flex-col items-center justify-center gap-4 md:flex-row"
         initial={{ opacity: 0, y: 20 }}
         animate={{ opacity: 1, y: 0 }}
         transition={{ duration: 0.5 }}
      >
         {/* <div className="w-full md:w-auto">
            <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
               <DropdownMenuTrigger asChild>
                  <Button
                     variant="outline"
                     className="w-full justify-between md:w-[200px]"
                  >
                     {selectedCategory ? (
                        <span className="capitalize">{selectedCategory}</span>
                     ) : (
                        "All Categories"
                     )}
                     <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
               </DropdownMenuTrigger>
               <DropdownMenuContent className="w-full md:w-[200px]">
                  <DropdownMenuGroup>
                     <DropdownMenuItem
                        onClick={() => {
                           onCategoryChange(null);
                           setIsOpen(false);
                        }}
                     >
                        <span>All Categories</span>
                        {selectedCategory === null && (
                           <Check className="ml-auto h-4 w-4" />
                        )}
                     </DropdownMenuItem>
                     {categories.map((category) => (
                        <DropdownMenuItem
                           key={category}
                           onClick={() => {
                              onCategoryChange(category);
                              setIsOpen(false);
                           }}
                        >
                           <span className="capitalize">{category}</span>
                           {selectedCategory === category && (
                              <Check className="ml-auto h-4 w-4" />
                           )}
                        </DropdownMenuItem>
                     ))}
                  </DropdownMenuGroup>
               </DropdownMenuContent>
            </DropdownMenu>
         </div> */}

         <div className="relative w-full md:w-auto md:max-w-md md:flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-muted-foreground" />
            <Input
               placeholder="Search products..."
               value={searchQuery}
               onChange={(e) => onSearchChange(e.target.value)}
               className="w-full pl-9"
            />
         </div>
      </motion.div>
   );
}
