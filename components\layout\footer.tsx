import { CONTACT_INFO, FOOTER_LINKS } from "@/constants";
import { Mail, MapPin, Phone } from "lucide-react";
import Link from "next/link";

export function Footer() {
   return (
      <footer className="bg-slate-900 text-slate-200">
         <div className="container py-12 md:py-16">
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 md:gap-12 lg:grid-cols-4">
               {/* Company Information */}
               <div className="space-y-4">
                  <h3 className="font-serif text-2xl font-semibold">
                     Tvine Stones
                  </h3>
                  <p className="text-slate-400">
                     Premium supplier of the finest Nigerian granite and quality
                     marble for homes, businesses, and architectural
                     masterpieces.
                  </p>
               </div>

               {/* Footer Links */}
               {FOOTER_LINKS.map((section) => (
                  <div key={section.title} className="space-y-4">
                     <h4 className="font-serif text-lg font-semibold">
                        {section.title}
                     </h4>
                     <ul className="space-y-2">
                        {section.links.map((link) => (
                           <li key={link.name}>
                              <Link
                                 href={link.href}
                                 className="text-sm text-slate-400 transition-colors hover:text-white"
                              >
                                 {link.name}
                              </Link>
                           </li>
                        ))}
                     </ul>
                  </div>
               ))}

               <div className="space-y-4">
                  <h4 className="font-serif text-lg font-semibold">
                     Contact Us
                  </h4>
                  <ul className="space-y-4">
                     <li className="flex items-center space-x-2 text-slate-400 hover:text-white">
                        <Phone className="h-4 w-4 shrink-0" />
                        <a
                           href={`tel:${CONTACT_INFO.phone}`}
                           className="text-sm transition-colors"
                        >
                           {CONTACT_INFO.phone}
                        </a>
                     </li>
                     <li className="flex items-center space-x-2 text-slate-400 hover:text-white">
                        <Mail className="h-4 w-4 shrink-0" />
                        <a
                           href={`mailto:${CONTACT_INFO.email}`}
                           className="text-sm transition-colors"
                        >
                           {CONTACT_INFO.email}
                        </a>
                     </li>
                     <li className="flex items-center space-x-2 text-slate-400 hover:text-white">
                        <MapPin className="h-4 w-4 shrink-0" />
                        <span className="text-sm">{CONTACT_INFO.address}</span>
                     </li>
                  </ul>
               </div>
            </div>

            <div className="mt-10 flex flex-col items-center justify-between gap-4 border-t border-slate-800 pt-6 md:flex-row">
               <p className="w-full text-center text-sm text-slate-400">
                  © {new Date().getFullYear()} Tvine Stones. All rights
                  reserved.
               </p>
            </div>
         </div>
      </footer>
   );
}
